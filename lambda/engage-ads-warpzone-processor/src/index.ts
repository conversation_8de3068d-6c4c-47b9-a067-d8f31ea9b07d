import type { Context, SQSEvent } from "aws-lambda";
import {
  connectToDatabase,
  WarpzoneModel,
  type WarpzoneEvent,
  type WarpzonePayload,
} from "@meltwater/engage-ads-db-sdk";
import { WarpzoneConsumer } from "@meltwater/grimoire-lambda-abstracts";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { toTimestampInMilliseconds } from "@meltwater/grimoire-commons";

const logger = LambdaLogger.getInstance();

class EngageAdsWarpzoneProcessor extends WarpzoneConsumer {
  public async handleMessage(event: WarpzoneEvent, attributes?: unknown): Promise<void> {
    logger.info("handleMessage called - Processing warpzone message with payload:", {
      "internal.engage.ads.payload": JSON.stringify(event),
      "internal.engage.ads.event_type": typeof event,
      "internal.engage.ads.event_keys": event ? Object.keys(event) : "null",
    });
    logger.debug("Message attributes:", { "internal.engage.ads.attributes": JSON.stringify(attributes) });

    try {
      // Ensure database connection
      await connectToDatabase();

      const isValidWarpzoneEvent = this.isWarpzoneEvent(event);
      logger.info("Warpzone event validation result", {
        "internal.engage.ads.is_valid": isValidWarpzoneEvent,
        "internal.engage.ads.event_id": event?.eventId,
        "internal.engage.ads.payload_type": event?.payloadType,
      });

      if (isValidWarpzoneEvent) {
        const payload = this.normalizeDates(event.payload);
        LambdaTracer.addCustomAttributes({
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });
        await WarpzoneModel.createPayload(payload);

        logger.info("Successfully stored warpzone payload", {
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });
      } else {
        throw new Error("Invalid payload format");
      }
    } catch (error) {
      logger.error("Error processing warpzone message", {
        error,
        "internal.engage.ads.payload": JSON.stringify(event),
      });
      throw error;
    }
  }

  private isWarpzoneEvent(payload: unknown): payload is WarpzoneEvent {
    if (typeof payload !== "object" || payload === null) {
      return false;
    }

    const event = payload as Partial<WarpzoneEvent>;
    return (
      typeof event.eventId === "string" &&
      typeof event.eventType === "string" &&
      typeof event.source === "string" &&
      typeof event.payloadType === "string" &&
      typeof event.payloadVersion === "number" &&
      typeof event.publisher === "string" &&
      typeof event.payload === "object" &&
      event.payload !== null &&
      Array.isArray(event.payloadEncoding) &&
      typeof event.version === "number" &&
      typeof event.publishedAt === "number"
    );
  }

  private normalizeDates(payload: WarpzonePayload): WarpzonePayload {
    if (payload?.document?.body?.publishDate?.date) {
      const date = payload.document.body.publishDate.date;
      payload.document.body.publishDate.date = toTimestampInMilliseconds(date);
    }
    return payload;
  }
}

const warpzoneProcessor = new EngageAdsWarpzoneProcessor();

// Direct handler for debugging - bypasses WarpzoneConsumer
export const directHandler = async (event: SQSEvent, context: Context) => {
  logger.info("Direct handler invoked", {
    "internal.engage.ads.records_count": event.Records?.length || 0,
  });

  for (const record of event.Records) {
    try {
      logger.info("Processing SQS record directly", {
        "internal.engage.ads.message_id": record.messageId,
        "internal.engage.ads.body_preview": record.body.substring(0, 200),
      });

      const parsedBody = JSON.parse(record.body);
      await warpzoneProcessor.handleMessage(parsedBody, record.attributes);

      logger.info("Successfully processed record", {
        "internal.engage.ads.message_id": record.messageId,
      });
    } catch (error) {
      logger.error("Failed to process record", {
        "internal.engage.ads.message_id": record.messageId,
        error,
      });
      throw error; // This will cause the entire batch to fail
    }
  }
};

export const handler = (event: SQSEvent, context: Context) => {
  logger.info("Lambda handler invoked", {
    "internal.engage.ads.records_count": event.Records?.length || 0,
    "internal.engage.ads.event_source": event.Records?.[0]?.eventSource,
  });

  // Log the raw SQS message body for debugging
  if (event.Records && event.Records.length > 0) {
    event.Records.forEach((record, index) => {
      logger.info(`SQS Record ${index}`, {
        "internal.engage.ads.message_id": record.messageId,
        "internal.engage.ads.body_preview": record.body.substring(0, 200),
        "internal.engage.ads.body_length": record.body.length,
      });
    });
  }

  // Use direct handler for debugging - comment this out to use WarpzoneConsumer
  // return directHandler(event, context);

  return warpzoneProcessor.handler(event, context);
};
