locals {
  warpzone_processor_lambda_name = "${local.resource_prefix}warpzone-processor-lambda"
}

data "aws_ecr_repository" "this" {
  name = "warpzone-processor"
}

module "this_lambda" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.4.0"

  function_name  = local.warpzone_processor_lambda_name
  description    = "Warpzone Processor Lambda for processing SQS messages"
  package_type   = "Image"
  image_uri      = "${data.aws_ecr_repository.this.repository_url}:latest"

  # Prevent the module from packaging code
  create_package = false

  # Configurations specific to container images
  memory_size    = var.memory_size
  timeout        = var.timeout
  publish        = true
  tracing_mode   = "Active"
  architectures  = ["arm64"]

  # VPC Configuration
  vpc_subnet_ids         = var.private_subnet_ids
  vpc_security_group_ids = var.security_group_id

  # Environment Variables
  environment_variables = {
    CX_DOMAIN                           = var.coralogix_domain
    CX_SECRET                           = var.coralogix_secret
    CX_TRACING_MODE                     = var.coralogix_tracing_mode
    CX_TAGS_ENABLED                     = var.coralogix_tags_enabled
    CX_APPLICATION                      = var.coralogix_application
    CX_SUB_SYSTEM                       = var.coralogix_sub_system
    CX_REPORTING_STRATEGY               = var.coralogix_reporting_strategy
    MONGODB_URI                         = var.mongodb_uri
    WARPZONE_PROCESSOR_QUEUE            = aws_sqs_queue.warpzone_processor_queue.url
    WARPZONE_PROCESSOR_DEADLETTER_QUEUE = aws_sqs_queue.warpzone_processor_dead_letter_queue.url
  }

  # IAM Policies
  attach_policies    = true
  policies = [
    "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "arn:aws:iam::aws:policy/AmazonSQSFullAccess",
    "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess",
  ]

  attach_policy_json = true
  policy_json = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface",
          "secretsmanager:GetSecretValue",
          "lambda:GetFunction",
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes",
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ],
        Resource = "*"
      }
    ]
  })

  # Event Source Mapping
  event_source_mapping = {
    sqs = {
      event_source_arn                = aws_sqs_queue.warpzone_processor_queue.arn
      batch_size                      = 10
      maximum_batching_window_in_seconds = 5
      function_response_types         = ["ReportBatchItemFailures"]
    }
  }
}

module "this_lambda_latest_alias" {
  source = "terraform-aws-modules/lambda/aws//modules/alias"

  name          = "latest"
  function_name = module.this_lambda.lambda_function_name
}
